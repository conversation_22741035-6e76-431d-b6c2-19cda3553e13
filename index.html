<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接红包小游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>🧧 接红包小游戏 🧧</h1>
            <div class="score-board">
                <span>得分: <span id="score">0</span></span>
                <span>生命: <span id="lives">3</span></span>
            </div>
        </div>
        
        <div class="game-area" id="gameArea">
            <!-- 精灵角色 -->
            <div class="player" id="player">🧙‍♂️</div>
            
            <!-- 游戏说明 -->
            <div class="instructions" id="instructions">
                <p>使用 ← → 方向键或 A D 键控制精灵移动</p>
                <p>接住红包得分，错过红包扣生命</p>
                <button id="startBtn">开始游戏</button>
            </div>
            
            <!-- 游戏结束界面 -->
            <div class="game-over" id="gameOver" style="display: none;">
                <h2>游戏结束</h2>
                <p>最终得分: <span id="finalScore">0</span></p>
                <button id="restartBtn">重新开始</button>
            </div>
        </div>
        
        <div class="controls">
            <p>控制说明：使用方向键 ← → 或 A D 键移动精灵</p>
        </div>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
