class RedPacketGame {
    constructor() {
        this.gameArea = document.getElementById('gameArea');
        this.player = document.getElementById('player');
        this.scoreElement = document.getElementById('score');
        this.livesElement = document.getElementById('lives');
        this.instructions = document.getElementById('instructions');
        this.gameOver = document.getElementById('gameOver');
        this.startBtn = document.getElementById('startBtn');
        this.restartBtn = document.getElementById('restartBtn');
        this.finalScore = document.getElementById('finalScore');
        
        this.gameWidth = this.gameArea.offsetWidth;
        this.gameHeight = this.gameArea.offsetHeight;
        this.playerWidth = 60;
        this.playerHeight = 60;
        
        this.score = 0;
        this.lives = 3;
        this.gameRunning = false;
        this.playerPosition = this.gameWidth / 2 - this.playerWidth / 2;
        this.redPackets = [];
        this.gameSpeed = 1;
        
        this.keys = {
            left: false,
            right: false
        };
        
        this.init();
    }
    
    init() {
        this.startBtn.addEventListener('click', () => this.startGame());
        this.restartBtn.addEventListener('click', () => this.restartGame());
        
        // 键盘事件监听
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // 触摸事件监听（移动端支持）
        this.gameArea.addEventListener('touchstart', (e) => this.handleTouch(e));
        this.gameArea.addEventListener('touchmove', (e) => this.handleTouch(e));
        
        this.updatePlayerPosition();
    }
    
    handleKeyDown(e) {
        switch(e.code) {
            case 'ArrowLeft':
            case 'KeyA':
                this.keys.left = true;
                e.preventDefault();
                break;
            case 'ArrowRight':
            case 'KeyD':
                this.keys.right = true;
                e.preventDefault();
                break;
        }
    }
    
    handleKeyUp(e) {
        switch(e.code) {
            case 'ArrowLeft':
            case 'KeyA':
                this.keys.left = false;
                break;
            case 'ArrowRight':
            case 'KeyD':
                this.keys.right = false;
                break;
        }
    }
    
    handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const rect = this.gameArea.getBoundingClientRect();
        const touchX = touch.clientX - rect.left;
        
        if (touchX < this.gameWidth / 2) {
            this.keys.left = true;
            this.keys.right = false;
        } else {
            this.keys.right = true;
            this.keys.left = false;
        }
        
        if (e.type === 'touchend') {
            this.keys.left = false;
            this.keys.right = false;
        }
    }
    
    startGame() {
        this.gameRunning = true;
        this.instructions.style.display = 'none';
        this.gameLoop();
        this.spawnRedPackets();
    }
    
    restartGame() {
        this.score = 0;
        this.lives = 3;
        this.gameSpeed = 1;
        this.playerPosition = this.gameWidth / 2 - this.playerWidth / 2;
        this.redPackets = [];
        
        // 清除所有红包
        const existingPackets = this.gameArea.querySelectorAll('.red-packet');
        existingPackets.forEach(packet => packet.remove());
        
        this.updateScore();
        this.updateLives();
        this.updatePlayerPosition();
        this.gameOver.style.display = 'none';
        this.startGame();
    }
    
    gameLoop() {
        if (!this.gameRunning) return;
        
        this.updatePlayer();
        this.updateRedPackets();
        this.checkCollisions();
        
        requestAnimationFrame(() => this.gameLoop());
    }
    
    updatePlayer() {
        const moveSpeed = 5;
        
        if (this.keys.left && this.playerPosition > 0) {
            this.playerPosition -= moveSpeed;
        }
        if (this.keys.right && this.playerPosition < this.gameWidth - this.playerWidth) {
            this.playerPosition += moveSpeed;
        }
        
        this.updatePlayerPosition();
    }
    
    updatePlayerPosition() {
        this.player.style.left = this.playerPosition + 'px';
    }
    
    spawnRedPackets() {
        if (!this.gameRunning) return;
        
        this.createRedPacket();
        
        // 随机间隔生成红包，游戏速度越快间隔越短
        const spawnDelay = Math.max(800 - this.gameSpeed * 50, 300);
        setTimeout(() => this.spawnRedPackets(), spawnDelay + Math.random() * 500);
    }
    
    createRedPacket() {
        const redPacket = document.createElement('div');
        redPacket.className = 'red-packet';
        redPacket.innerHTML = '🧧';
        
        const x = Math.random() * (this.gameWidth - 40);
        redPacket.style.left = x + 'px';
        redPacket.style.top = '-50px';
        
        const fallDuration = Math.max(3000 - this.gameSpeed * 100, 1500);
        redPacket.style.animationDuration = fallDuration + 'ms';
        
        this.gameArea.appendChild(redPacket);
        this.redPackets.push({
            element: redPacket,
            x: x,
            y: -50,
            speed: this.gameHeight / (fallDuration / 16.67) // 像素每帧
        });
        
        // 红包掉落完成后移除
        setTimeout(() => {
            if (redPacket.parentNode) {
                redPacket.remove();
                this.redPackets = this.redPackets.filter(packet => packet.element !== redPacket);
                this.loseLife();
            }
        }, fallDuration);
    }
    
    updateRedPackets() {
        this.redPackets.forEach(packet => {
            packet.y += packet.speed;
            packet.element.style.top = packet.y + 'px';
        });
    }
    
    checkCollisions() {
        this.redPackets.forEach((packet, index) => {
            const packetRect = {
                x: packet.x,
                y: packet.y,
                width: 40,
                height: 40
            };
            
            const playerRect = {
                x: this.playerPosition,
                y: this.gameHeight - 70,
                width: this.playerWidth,
                height: this.playerHeight
            };
            
            if (this.isColliding(packetRect, playerRect)) {
                this.collectRedPacket(packet, index);
            }
        });
    }
    
    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }
    
    collectRedPacket(packet, index) {
        packet.element.remove();
        this.redPackets.splice(index, 1);
        this.score += 10;
        this.updateScore();
        
        // 每100分增加游戏速度
        if (this.score % 100 === 0) {
            this.gameSpeed++;
        }
        
        // 添加收集效果
        this.showCollectEffect(packet.x, packet.y);
    }
    
    showCollectEffect(x, y) {
        const effect = document.createElement('div');
        effect.innerHTML = '+10';
        effect.style.position = 'absolute';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.color = '#ff6b6b';
        effect.style.fontWeight = 'bold';
        effect.style.fontSize = '1.5em';
        effect.style.zIndex = '15';
        effect.style.pointerEvents = 'none';
        effect.style.animation = 'fadeUp 1s ease-out forwards';
        
        this.gameArea.appendChild(effect);
        
        setTimeout(() => effect.remove(), 1000);
    }
    
    loseLife() {
        this.lives--;
        this.updateLives();
        
        if (this.lives <= 0) {
            this.endGame();
        }
    }
    
    updateScore() {
        this.scoreElement.textContent = this.score;
    }
    
    updateLives() {
        this.livesElement.textContent = this.lives;
    }
    
    endGame() {
        this.gameRunning = false;
        this.finalScore.textContent = this.score;
        this.gameOver.style.display = 'block';
        
        // 清除所有红包
        const existingPackets = this.gameArea.querySelectorAll('.red-packet');
        existingPackets.forEach(packet => packet.remove());
        this.redPackets = [];
    }
}

// 添加淡出上升动画
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeUp {
        0% {
            opacity: 1;
            transform: translateY(0);
        }
        100% {
            opacity: 0;
            transform: translateY(-30px);
        }
    }
`;
document.head.appendChild(style);

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new RedPacketGame();
});
