* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    color: #d63384;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.score-board {
    display: flex;
    justify-content: space-around;
    font-size: 1.2em;
    font-weight: bold;
    color: #495057;
}

.game-area {
    position: relative;
    width: 100%;
    height: 500px;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
    border: 3px solid #ffc107;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 20px;
}

.player {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 3em;
    z-index: 10;
    transition: left 0.1s ease;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.red-packet {
    position: absolute;
    font-size: 2em;
    z-index: 5;
    animation: fall linear;
    filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.2));
}

@keyframes fall {
    from {
        top: -50px;
    }
    to {
        top: 100%;
    }
}

.instructions, .game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 20;
}

.instructions p, .game-over p {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.1em;
}

.instructions h2, .game-over h2 {
    color: #d63384;
    margin-bottom: 20px;
    font-size: 2em;
}

button {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.2s ease;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

button:active {
    transform: translateY(0);
}

.controls {
    text-align: center;
    color: #495057;
    font-size: 0.9em;
    background: rgba(255, 255, 255, 0.7);
    padding: 10px;
    border-radius: 10px;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    .game-area {
        height: 400px;
    }
    
    .player {
        font-size: 2.5em;
    }
    
    .red-packet {
        font-size: 1.5em;
    }
}
